
import 'dart:collection';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/utils/lru_cache.dart';
import 'package:get/get.dart';

/// 简化的弱引用回调包装类
class WeakCallback<T> {
  final WeakReference<Function(T)>? _callbackRef;

  WeakCallback(Function(T) callback) : _callbackRef = WeakReference(callback);

  /// 调用回调函数，如果回调已被回收则返回false
  bool call(T argument) {
    final callback = _callbackRef?.target;
    if (callback != null) {
      try {
        callback(argument);
        return true;
      } catch (e) {
        LogUtil.error('WeakCallback调用异常: $e');
        return false;
      }
    }
    return false;
  }

  /// 检查回调是否仍然有效
  bool get isValid => _callbackRef?.target != null;
}

/// 简化的取消令牌类
class CancellationToken {
  bool _isCancelled = false;

  /// 是否已取消
  bool get isCancelled => _isCancelled;

  /// 取消操作
  void cancel() {
    _isCancelled = true;
  }

  /// 检查是否已取消，如果已取消则抛出异常
  void throwIfCancelled() {
    if (_isCancelled) {
      throw OperationCanceledException('操作已取消');
    }
  }
}

/// 操作取消异常
class OperationCanceledException implements Exception {
  final String message;
  OperationCanceledException(this.message);

  @override
  String toString() => 'OperationCanceledException: $message';
}

/// 资源跟踪器
///
/// 跟踪所有需要清理的资源，确保完整清理
class ResourceTracker {
  final Map<String, dynamic> _resources = {};
  final Map<String, VoidCallback> _cleanupCallbacks = {};
  bool _isDisposed = false;

  /// 注册资源
  void registerResource(String key, dynamic resource, VoidCallback cleanupCallback) {
    if (_isDisposed) {
      LogUtil.warn('ResourceTracker已释放，无法注册资源: $key');
      cleanupCallback();
      return;
    }

    _resources[key] = resource;
    _cleanupCallbacks[key] = cleanupCallback;
    LogUtil.debug('已注册资源: $key (${resource.runtimeType})');
  }

  /// 注销资源
  void unregisterResource(String key) {
    if (_resources.containsKey(key)) {
      _resources.remove(key);
      _cleanupCallbacks.remove(key);
      LogUtil.debug('已注销资源: $key');
    }
  }

  /// 清理指定资源
  bool cleanupResource(String key) {
    final callback = _cleanupCallbacks[key];
    if (callback != null) {
      try {
        callback();
        unregisterResource(key);
        return true;
      } catch (e) {
        LogUtil.error('资源清理异常: $key, 错误: $e');
        return false;
      }
    }
    return false;
  }

  /// 清理所有资源
  void cleanupAll() {
    if (_isDisposed) return;

    LogUtil.debug('开始清理所有资源，共${_resources.length}个');
    final keys = _cleanupCallbacks.keys.toList();

    for (final key in keys) {
      cleanupResource(key);
    }

    _isDisposed = true;
    LogUtil.debug('ResourceTracker已释放');
  }

  /// 获取资源统计
  Map<String, dynamic> getStats() {
    return {
      'totalResources': _resources.length,
      'isDisposed': _isDisposed,
      'resourceTypes': _resources.values.map((r) => r.runtimeType.toString()).toList(),
    };
  }

  /// 检查是否有未清理的资源
  bool get hasUncleanedResources => !_isDisposed && _resources.isNotEmpty;

  /// 获取未清理的资源列表
  List<String> get uncleanedResourceKeys => _resources.keys.toList();
}

/// 图片预加载优先级
enum ImagePreloadPriority {
  /// 高优先级 - 立即加载
  high,
  
  /// 中优先级 - 当高优先级加载完成后加载
  medium,
  
  /// 低优先级 - 仅在空闲时加载
  low
}

/// 图片预加载质量
enum ImagePreloadQuality {
  /// 原始质量
  original,
  
  /// 高质量
  high,
  
  /// 中等质量
  medium,
  
  /// 低质量 (缩略图)
  low
}

/// 图片预加载项
class PreloadItem {
  final String url;
  final ImagePreloadPriority priority;
  final ImagePreloadQuality quality;
  final int? width;
  final int? height;
  final double score; // 预测分数，用于确定加载顺序
  final WeakCallback<bool>? onComplete; // 加载完成回调（弱引用）

  PreloadItem({
    required this.url,
    this.priority = ImagePreloadPriority.medium,
    this.quality = ImagePreloadQuality.original,
    this.width,
    this.height,
    this.score = 0.0,
    this.onComplete,
  });

  /// 从普通回调函数创建 PreloadItem
  PreloadItem.withCallback({
    required this.url,
    this.priority = ImagePreloadPriority.medium,
    this.quality = ImagePreloadQuality.original,
    this.width,
    this.height,
    this.score = 0.0,
    Function(bool success)? callback,
  }) : onComplete = callback != null ? WeakCallback<bool>(callback) : null;
}

/// 图片预加载工具类
///
/// 用于提前加载图片资源，减少用户等待时间
class ImagePreloader extends GetxController {
  /// 静态 Finalizer，用于自动清理资源
  static final Finalizer<ResourceTracker> _finalizer = Finalizer((tracker) {
    LogUtil.warn('ImagePreloader被垃圾回收，执行自动资源清理');
    tracker.cleanupAll();
  });

  /// 资源跟踪器
  late final ResourceTracker _resourceTracker;

  /// 主取消令牌，用于取消所有异步操作
  late final CancellationToken _mainCancellationToken;

  /// 图片LRU缓存
  late final ImageLRUCache _imageCache;

  /// 正在加载的图片URL集合
  final Set<String> _loadingImages = <String>{};

  /// 最大并发加载数
  final int _maxConcurrentLoads = 8;
  
  /// 优先级队列 - 高优先级
  final Queue<PreloadItem> _highPriorityQueue = Queue<PreloadItem>();
  
  /// 优先级队列 - 中优先级
  final Queue<PreloadItem> _mediumPriorityQueue = Queue<PreloadItem>();
  
  /// 优先级队列 - 低优先级
  final Queue<PreloadItem> _lowPriorityQueue = Queue<PreloadItem>();
  
  /// 网络状态
  bool _isOnWifi = true; // 默认假设在WiFi环境
  
  /// 设备内存压力指示器
  bool _isLowMemory = false;
  
  /// 是否正在处理队列
  bool _isProcessingQueue = false;
  
  /// 缓存管理器
  late final CacheManager _cacheManager;
  
  /// 去重LRU缓存 - 记录短时间内处理过的URL，避免重复加载
  late final LRUCache<String, int> _recentlyProcessed;
  
  /// 批处理触发器 - 用于GetX响应式批处理
  final RxInt _batchTrigger = 0.obs;

  /// 批处理队列 - 用于合并多次预加载请求
  final List<PreloadItem> _batchQueue = [];
  
  /// 批处理队列最大容量
  static const int _maxBatchQueueSize = 500;

  /// 批处理锁 - 防止并发访问
  bool _isBatchProcessing = false;



  /// GetX Workers列表 - 用于管理所有Worker
  final List<Worker> _workers = [];
  
  /// Worker状态标记，用于防止重复清理
  bool _workersDisposed = false;

  /// 构造函数
  ImagePreloader() {
    // 初始化资源跟踪器和取消令牌
    _resourceTracker = ResourceTracker();
    _mainCancellationToken = CancellationToken();

    // 注册 Finalizer
    _finalizer.attach(this, _resourceTracker, detach: this);

    _cacheManager = Get.find<CacheManager>();
    _detectNetworkType();

    // 初始化图片LRU缓存
    _imageCache = ImageLRUCache(
      maxCapacity: 500, // 最大缓存500张图片
      maxSizeBytes: 100 * 1024 * 1024, // 最大100MB
      defaultTtl: 30 * 60 * 1000, // 默认30分钟过期
      enableStats: true,
    );

    // 初始化去重LRU缓存
    _recentlyProcessed = LRUCache<String, int>(
      maxCapacity: 1000, // 最大记录1000条URL
      defaultTtl: 60 * 1000, // 默认60秒过期
      enableStats: false, // 不需要统计信息
    );

    // 注册核心资源到跟踪器
    _resourceTracker.registerResource('imageCache', _imageCache, () => _imageCache.clear());
    _resourceTracker.registerResource('recentlyProcessed', _recentlyProcessed, () => _recentlyProcessed.clear());
    _resourceTracker.registerResource('mainCancellationToken', _mainCancellationToken, () => _mainCancellationToken.cancel());
  }

  @override
  void onInit() {
    super.onInit();
    _setupPeriodicProcessing();
    _setupCleanupTask();
    _setupBatchProcessing();
    LogUtil.debug('ImagePreloader初始化完成');
  }

  /// 设置批处理防抖机制
  void _setupBatchProcessing() {
    // 使用GetX的debounce替代Timer防抖
    final batchWorker = debounce(_batchTrigger, (value) => _processBatchQueue(), time: const Duration(milliseconds: 100));
    _addWorker(batchWorker);
  }

  @override
  void onClose() {
    // 取消主取消令牌，这会级联取消所有子操作
    _mainCancellationToken.cancel();

    // 清理所有Workers
    _disposeWorkers();

    // 清理GetX响应式变量
    _batchTrigger.close();

    // 清理所有缓存和队列
    _clearAllResources();

    // 使用资源跟踪器进行最终清理
    _resourceTracker.cleanupAll();

    // 从 Finalizer 中分离
    _finalizer.detach(this);

    super.onClose();
    LogUtil.debug('ImagePreloader资源清理完成');
  }
  
  /// 安全清理所有Workers
  void _disposeWorkers() {
    if (_workersDisposed) {
      LogUtil.warn('Workers已经被清理，跳过重复清理');
      return;
    }
    
    _workersDisposed = true;
    
    // 逐个清理Worker，并捕获可能的异常
    for (int i = 0; i < _workers.length; i++) {
      try {
        final worker = _workers[i];
        if (!worker.disposed) {
          worker.dispose();
        }
      } catch (e) {
        LogUtil.error('Worker清理异常 (index: $i): $e');
      }
    }
    
    _workers.clear();
    LogUtil.debug('所有Workers已安全清理');
  }

  /// 设置清理任务 - LRU缓存会自动处理过期清理
  void _setupCleanupTask() {
    // LRU缓存会自动清理过期项，无需额外的定时清理
    // 只在内存压力大时手动触发清理
    final cleanupWorker = debounce(
      _batchTrigger,
      (_) => _performMemoryCleanup(),
      time: const Duration(minutes: 5), // 延长到5分钟，减少频繁清理
    );
    _addWorker(cleanupWorker);
  }
  
  /// 执行内存清理
  void _performMemoryCleanup() {
    if (_isLowMemory) {
      // 清理过期的URL去重缓存
      final expiredCount = _recentlyProcessed.cleanupExpired();
      
      // 清理图片缓存中的过期项
      final imageExpiredCount = _imageCache.cleanupExpired();
      
      if (expiredCount > 0 || imageExpiredCount > 0) {
        LogUtil.debug('内存清理完成：URL去重缓存清理${expiredCount}条，图片缓存清理${imageExpiredCount}条');
      }
    }
  }

  /// 设置定期处理队列
  void _setupPeriodicProcessing() {
    // 使用GetX的interval Worker替代Stream.periodic
    // 创建一个响应式变量用于触发队列处理
    final queueTrigger = 0.obs;

    final queueWorker = ever(queueTrigger, (_) {
      if (!_isProcessingQueue && _loadingImages.length < _maxConcurrentLoads) {
        _processQueue();
      }
    });
    _addWorker(queueWorker);

    // 使用Future.delayed递归调用替代Timer
    _startQueueProcessing(queueTrigger);
  }
  
  /// 统一添加Worker的方法
  void _addWorker(Worker worker) {
    if (!_workersDisposed) {
      _workers.add(worker);

      // 注册到资源跟踪器
      final workerId = 'worker_${worker.hashCode}';
      _resourceTracker.registerResource(workerId, worker, () {
        if (!worker.disposed) {
          worker.dispose();
        }
      });
    } else {
      // 如果已经被清理，直接释放Worker
      worker.dispose();
      LogUtil.warn('Worker已被清理，新Worker被直接释放');
    }
  }

  /// 启动队列处理循环
  void _startQueueProcessing(RxInt trigger) {
    _scheduleNextProcessing(trigger);
  }

  /// 调度下一次队列处理
  void _scheduleNextProcessing(RxInt trigger) {
    Future.delayed(const Duration(seconds: 1), () {
      if (!isClosed && !_mainCancellationToken.isCancelled) {
        trigger.value++;
        _scheduleNextProcessing(trigger);
      }
    });
  }
  
  /// 检测网络类型 (WiFi或移动网络)
  void _detectNetworkType() {
    // 在实际实现中应使用连接检测包如connectivity_plus
    // 这里简化实现
    _isOnWifi = true;
  }
  
  /// 根据网络类型和内存状态确定最大并发数
  int get _effectiveMaxConcurrentLoads {
    if (_isLowMemory) {
      return 2; // 内存压力大时减少并发
    }
    return _isOnWifi ? _maxConcurrentLoads : 3; // WiFi下并发更多，移动网络少一些
  }
  
  /// 预加载单个图片
  /// 
  /// [url] 图片URL
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [priority] 加载优先级
  /// [quality] 加载质量
  /// [onComplete] 加载完成回调
  Future<void> preloadImage(
    String url, {
    int? width,
    int? height, 
    ImagePreloadPriority priority = ImagePreloadPriority.medium,
    ImagePreloadQuality quality = ImagePreloadQuality.original,
    Function(bool success)? onComplete
  }) async {
    // 验证URL有效性
    if (!_validateImageUrl(url, onComplete)) {
      return;
    }
    
    final cacheKey = _generateCacheKey(url, quality, width, height);
    
    // 检查重复处理
    if (_isRecentlyProcessed(cacheKey, onComplete)) {
      return;
    }
    
    // 记录处理时间
    _recordProcessTime(cacheKey);
    
    // 检查缓存和加载状态
    if (_isAlreadyCachedOrLoading(url, width, height, cacheKey, onComplete)) {
      return;
    }
    
    // 创建并处理预加载项
    final item = _createPreloadItem(url, width, height, priority, quality, onComplete);
    _processPreloadItem(item, priority);
  }
  
  /// 验证图片URL有效性
  bool _validateImageUrl(String url, Function(bool success)? onComplete) {
    if (url.isEmpty) {
      LogUtil.warn('预加载图片失败：URL为空');
      if (onComplete != null) onComplete(false);
      return false;
    }
    return true;
  }
  
  /// 检查是否最近已处理
  bool _isRecentlyProcessed(String cacheKey, Function(bool success)? onComplete) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastProcessTime = _recentlyProcessed.get(cacheKey);
    if (lastProcessTime != null && now - lastProcessTime < 1000) {
      LogUtil.debug('跳过重复预加载请求: $cacheKey (重复间隔: ${now - lastProcessTime}ms)');
      if (onComplete != null) onComplete(true);
      return true;
    }
    return false;
  }
  
  /// 记录处理时间
  void _recordProcessTime(String cacheKey) {
    _recentlyProcessed.put(cacheKey, DateTime.now().millisecondsSinceEpoch);
  }
  
  /// 检查是否已缓存或正在加载
  bool _isAlreadyCachedOrLoading(
    String url, 
    int? width, 
    int? height, 
    String cacheKey, 
    Function(bool success)? onComplete
  ) {
    if (_imageCache.hasImage(url, width: width, height: height)) {
      if (onComplete != null) onComplete(true);
      return true;
    }
    
    if (_loadingImages.contains(cacheKey)) {
      return true;
    }
    
    return false;
  }
  
  /// 创建预加载项
  PreloadItem _createPreloadItem(
    String url,
    int? width,
    int? height,
    ImagePreloadPriority priority,
    ImagePreloadQuality quality,
    Function(bool success)? onComplete
  ) {
    return PreloadItem.withCallback(
      url: url,
      priority: priority,
      quality: quality,
      width: width,
      height: height,
      score: _calculateItemScore(url, priority),
      callback: onComplete,
    );
  }
  
  /// 处理预加载项
  void _processPreloadItem(PreloadItem item, ImagePreloadPriority priority) {
    // 高优先级图片立即加载
    if (priority == ImagePreloadPriority.high) {
      _loadImageItem(item);
      return;
    }
    
    // 其他优先级图片进入队列
    _addToQueue(item, priority);
    
    // 如果有空闲容量，处理队列
    if (_loadingImages.length < _effectiveMaxConcurrentLoads) {
      _processQueue();
    }
  }
  
  /// 添加到相应优先级队列
  void _addToQueue(PreloadItem item, ImagePreloadPriority priority) {
    switch (priority) {
      case ImagePreloadPriority.high:
        // 高优先级在前面已经处理
        break;
      case ImagePreloadPriority.medium:
        _mediumPriorityQueue.add(item);
        break;
      case ImagePreloadPriority.low:
        _lowPriorityQueue.add(item);
        break;
    }
  }
  
  /// 计算预加载项的优先级分数
  double _calculateItemScore(String url, ImagePreloadPriority priority) {
    double baseScore;
    switch (priority) {
      case ImagePreloadPriority.high:
        baseScore = 100.0;
        break;
      case ImagePreloadPriority.medium:
        baseScore = 50.0;
        break;
      case ImagePreloadPriority.low:
        baseScore = 10.0;
        break;
    }
    
    // 可以基于历史访问模式、屏幕位置等调整分数
    // 这里使用一个简单的实现
    return baseScore;
  }
  
  /// 生成缓存键
  String _generateCacheKey(String url, ImagePreloadQuality quality, int? width, int? height) {
    if (quality == ImagePreloadQuality.original) {
      return url;
    }
    
    return '$url#quality=${quality.toString()}#width=${width ?? "auto"}#height=${height ?? "auto"}';
  }
  
  /// 基于质量级别获取调整后的URL
  /// 在实际应用中可以连接到图片调整服务如Cloudinary或本地图片服务
  String _getQualityAdjustedUrl(String url, ImagePreloadQuality quality, int? width, int? height) {
    // 简化实现 - 在实际应用中应替换为真正的图片优化服务调用
    switch (quality) {
      case ImagePreloadQuality.low:
        // 例如：使用缩略图URL
        return url; // 实际应用中这里应返回缩略图URL
      case ImagePreloadQuality.medium:
        return url; // 中等质量URL
      case ImagePreloadQuality.high:
      case ImagePreloadQuality.original:
        return url; // 原始URL
    }
  }
  
  /// 预加载多个图片
  /// 
  /// [urls] 图片URL列表
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [priority] 加载优先级
  /// [quality] 加载质量
  Future<void> preloadImages(
    List<String> urls, {
    int? width,
    int? height,
    ImagePreloadPriority priority = ImagePreloadPriority.medium,
    ImagePreloadQuality quality = ImagePreloadQuality.original
  }) async {
    if (urls.isEmpty) {
      return;
    }
    
    // 过滤掉无效URL和重复URL
    final validUrls = urls
        .where((url) => url.isNotEmpty)
        .toSet() // 使用Set去重
        .toList();
    
    if (validUrls.isEmpty) {
      return;
    }
    
    // 记录添加到队列的数量，而非重复调用日志
    LogUtil.debug('添加${validUrls.length}张图片到预加载队列，优先级: $priority');
    
    // 检查并保证批处理队列大小
    _ensureBatchQueueSize();
    
    // 将图片添加到批处理队列
    for (final url in validUrls) {
      // 再次检查以防添加过程中超出限制
      if (_batchQueue.length >= _maxBatchQueueSize) {
        LogUtil.warn('批处理队列已满，跳过剩余${validUrls.length - _batchQueue.length}个项目');
        break;
      }
      
      _batchQueue.add(
        PreloadItem(
          url: url,
          width: width,
          height: height,
          priority: priority,
          quality: quality
        )
      );
    }

    // 触发GetX防抖处理
    _batchTrigger.value++;
  }
  
  /// 处理批次队列
  void _processBatchQueue() async {
    if (_isBatchProcessing || _batchQueue.isEmpty) return;
    
    _isBatchProcessing = true;
    
    try {
      // 检查队列容量限制
      _ensureBatchQueueSize();
      
      // 创建URL去重集合
      final uniqueUrls = <String>{};
      final batch = <PreloadItem>[];
      
      // 过滤重复URL
      for (final item in _batchQueue) {
        final cacheKey = _generateCacheKey(item.url, item.quality, item.width, item.height);
        if (!uniqueUrls.contains(cacheKey)) {
          uniqueUrls.add(cacheKey);
          batch.add(item);
        }
      }
      
      // 清空批次队列
      _batchQueue.clear();
      
      // 记录实际添加的图片数量
      final filteredCount = batch.length;
      if (filteredCount > 0 && filteredCount < uniqueUrls.length) {
        LogUtil.debug('过滤后实际预加载${filteredCount}张唯一图片（移除了${uniqueUrls.length - filteredCount}张重复图片）');
      }
      
      // 处理批次中的每个图片
      for (final item in batch) {
        await preloadImage(
          item.url,
          width: item.width,
          height: item.height,
          priority: item.priority,
          quality: item.quality
        );
      }
    } catch (e) {
      LogUtil.error('批处理队列处理异常: $e');
      // 异常情况下也要清空队列，防止积累
      _batchQueue.clear();
    } finally {
      _isBatchProcessing = false;
    }
  }
  
  /// 确保批处理队列大小不超过限制
  void _ensureBatchQueueSize() {
    if (_batchQueue.length > _maxBatchQueueSize) {
      // 移除最旧的项目，保留最新的
      final removeCount = _batchQueue.length - _maxBatchQueueSize;
      _batchQueue.removeRange(0, removeCount);
      LogUtil.warn('批处理队列超出限制，已移除${removeCount}个最旧项目');
    }
  }
  
  /// 智能预加载 - 基于用户可能需要的图片
  /// 
  /// [visibleUrls] 当前可见的图片URL列表
  /// [potentialUrls] 可能即将看到的图片URL列表
  Future<void> smartPreload({
    required List<String> visibleUrls,
    required List<String> potentialUrls,
  }) async {
    // 可见的图片高优先级加载
    await preloadImages(
      visibleUrls,
      priority: ImagePreloadPriority.high,
      quality: _isOnWifi ? ImagePreloadQuality.original : ImagePreloadQuality.high,
    );
    
    // 可能看到的图片中优先级加载
    await preloadImages(
      potentialUrls,
      priority: ImagePreloadPriority.medium,
      quality: _isOnWifi ? ImagePreloadQuality.high : ImagePreloadQuality.medium,
    );
  }
  
  /// 处理加载队列
  Future<void> _processQueue() async {
    if (_isProcessingQueue) {
      return;
    }
    
    _isProcessingQueue = true;
    
    try {
      // 如果队列为空，直接返回
      if (_highPriorityQueue.isEmpty && _mediumPriorityQueue.isEmpty && _lowPriorityQueue.isEmpty) {
        return;
      }
      
      // 计算可以加载的数量
      final availableSlots = _effectiveMaxConcurrentLoads - _loadingImages.length;
      
      // 如果没有可用槽位，直接返回
      if (availableSlots <= 0) {
        return;
      }
      
      int slotsUsed = 0;
      
      // 优先处理高优先级队列
      while (slotsUsed < availableSlots && _highPriorityQueue.isNotEmpty) {
        final item = _highPriorityQueue.removeFirst();
        await _loadImageItem(item);
        slotsUsed++;
      }
      
      // 处理中优先级队列
      while (slotsUsed < availableSlots && _mediumPriorityQueue.isNotEmpty) {
        final item = _mediumPriorityQueue.removeFirst();
        await _loadImageItem(item);
        slotsUsed++;
      }
      
      // 如果还有空闲槽位且不在移动网络上，处理低优先级队列
      if (_isOnWifi) {
        while (slotsUsed < availableSlots && _lowPriorityQueue.isNotEmpty) {
          final item = _lowPriorityQueue.removeFirst();
          await _loadImageItem(item);
          slotsUsed++;
        }
      }
    } finally {
      _isProcessingQueue = false;
    }
  }
  
  /// 加载单个图片项
  Future<void> _loadImageItem(PreloadItem item) async {
    final cacheKey = _generateCacheKey(item.url, item.quality, item.width, item.height);
    
    // 检查是否可以加载
    if (!_canLoadImage(cacheKey)) {
      return;
    }
    
    try {
      _loadingImages.add(cacheKey);
      
      // 检查缓存中是否已存在
      if (await _tryLoadFromCache(item, cacheKey)) {
        return;
      }
      
      // 从网络加载图片
      await _loadImageFromNetwork(item, cacheKey);
      
    } catch (e) {
      _handleLoadError(item, cacheKey, e);
    }
  }
  
  /// 从网络加载图片
  Future<void> _loadImageFromNetwork(PreloadItem item, String cacheKey) async {
    // 检查是否已取消
    if (_mainCancellationToken.isCancelled) {
      throw OperationCanceledException('图片加载已取消');
    }

    final adjustedUrl = _getQualityAdjustedUrl(item.url, item.quality, item.width, item.height);
    final provider = CachedNetworkImageProvider(adjustedUrl);

    final configuration = _createImageConfiguration(item);
    final imageStream = provider.resolve(configuration);

    // 等待图片加载完成
    bool isLoadComplete = false;
    bool hasError = false;
    String? errorMessage;

    late ImageStreamListener listener;
    listener = ImageStreamListener(
      (ImageInfo imageInfo, bool synchronousCall) {
        if (!_mainCancellationToken.isCancelled) {
          _handleImageLoadSuccess(item, cacheKey, imageStream);
        }
        imageStream.removeListener(listener);
        isLoadComplete = true;
      },
      onError: (dynamic exception, StackTrace? stackTrace) {
        if (!_mainCancellationToken.isCancelled) {
          _handleImageLoadError(item, cacheKey, imageStream, exception);
        }
        imageStream.removeListener(listener);
        hasError = true;
        errorMessage = exception.toString();
      },
    );

    imageStream.addListener(listener);

    // 等待加载完成或出错
    while (!isLoadComplete && !hasError && !_mainCancellationToken.isCancelled) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    // 如果被取消，确保清理监听器
    if (_mainCancellationToken.isCancelled) {
      imageStream.removeListener(listener);
      throw OperationCanceledException('图片加载已取消');
    }

    if (hasError) {
      throw Exception(errorMessage ?? 'Image preload failed');
    }
  }
  
  /// 检查是否可以加载图片
  bool _canLoadImage(String cacheKey) {
    return !_imageCache.containsKey(cacheKey) && !_loadingImages.contains(cacheKey);
  }
  
  /// 尝试从缓存加载图片
  Future<bool> _tryLoadFromCache(PreloadItem item, String cacheKey) async {
    final bool existsInCache = await _checkImageInCache(cacheKey);
    if (existsInCache) {
      _imageCache.putImage(
        item.url,
        width: item.width,
        height: item.height,
        isPreloaded: true,
      );
      _completeImageLoad(item, cacheKey, true);
      return true;
    }
    return false;
  }
  
  /// 创建图片配置
  ImageConfiguration _createImageConfiguration(PreloadItem item) {
    return ImageConfiguration(
      size: item.width != null && item.height != null 
          ? Size(item.width!.toDouble(), item.height!.toDouble()) 
          : null,
    );
  }
  
  /// 处理图片加载成功
  void _handleImageLoadSuccess(
    PreloadItem item,
    String cacheKey,
    ImageStream imageStream
  ) {
    _imageCache.putImage(
      item.url,
      width: item.width,
      height: item.height,
      isPreloaded: true,
    );
    _completeImageLoad(item, cacheKey, true);
  }

  /// 处理图片加载错误
  void _handleImageLoadError(
    PreloadItem item,
    String cacheKey,
    ImageStream imageStream,
    dynamic exception
  ) {
    LogUtil.error('图片预加载失败: $cacheKey, 错误: $exception');
    _completeImageLoad(item, cacheKey, false);
  }
  
  /// 完成图片加载
  void _completeImageLoad(PreloadItem item, String cacheKey, bool success) {
    _loadingImages.remove(cacheKey);
    _processQueue();

    // 安全调用弱引用回调
    if (item.onComplete != null) {
      final callbackSuccess = item.onComplete!.call(success);
      if (!callbackSuccess) {
        LogUtil.debug('图片加载回调目标已被回收: $cacheKey');
      }
    }
  }
  
  /// 处理加载错误
  void _handleLoadError(PreloadItem item, String cacheKey, dynamic error) {
    _loadingImages.remove(cacheKey);
    LogUtil.error('图片预加载异常: $cacheKey, 错误: $error');
    _completeImageLoad(item, cacheKey, false);
  }
  
  /// 检查图片是否已在缓存中
  Future<bool> _checkImageInCache(String cacheKey) async {
    // 使用CacheManager检查图片是否已缓存
    try {
      final exists = await _cacheManager.exists(
        'image_$cacheKey', 
        strategy: CacheStrategy.memoryThenPersistent
      );
      return exists;
    } catch (e) {
      return false;
    }
  }
  
  /// 清除预加载缓存
  void clearMemoryCache() {
    final beforeReport = _generateMemoryReport();
    
    _imageCache.clear();
    _highPriorityQueue.clear();
    _mediumPriorityQueue.clear();
    _lowPriorityQueue.clear();
    _batchQueue.clear();
    _recentlyProcessed.clear();
    
    LogUtil.debug('已清除图片预加载内存缓存, 清理前: $beforeReport');
        
    // 检查是否完全清理
    _checkMemoryLeaks('clearMemoryCache');
  }
  
  /// 检查内存泄漏的调试方法
  void _checkMemoryLeaks(String context) {
    final leaks = <String>[];
    
    if (_imageCache.length > 0) {
      leaks.add('图片缓存未清空: ${_imageCache.length}项');
    }
    if (_recentlyProcessed.length > 0) {
      leaks.add('去重缓存未清空: ${_recentlyProcessed.length}项');
    }
    if (_loadingImages.isNotEmpty) {
      leaks.add('加载中集合未清空: ${_loadingImages.length}项');
    }
    if (_batchQueue.isNotEmpty) {
      leaks.add('批处理队列未清空: ${_batchQueue.length}项');
    }
    if (queueCount > 0) {
      leaks.add('优先级队列未清空: ${queueCount}项');
    }
    if (_workers.isNotEmpty && !_workersDisposed) {
      leaks.add('Workers未清理: ${_workers.length}个');
    }
    
    if (leaks.isNotEmpty) {
      LogUtil.warn('[$context] 可能的内存泄漏: ${leaks.join(", ")}');
    } else {
      LogUtil.debug('[$context] 内存泄漏检查通过');
    }
  }
  
  /// 生成内存使用详细报告用于调试
  String generateMemoryReport() {
    final stats = getMemoryStats();
    final report = StringBuffer();
    
    report.writeln('=== ImagePreloader 内存使用报告 ===');
    
    // 图片缓存
    final imageCache = stats['imageCache'] as Map<String, dynamic>;
    report.writeln('图片缓存: ${imageCache['count']}/${imageCache['maxCapacity']} '
        '(${((imageCache['sizeBytes'] as int) / 1024 / 1024).toStringAsFixed(1)}MB), '
        '已预加载: ${imageCache['preloadedCount']}');
    
    // 去重缓存
    final recentlyProcessed = stats['recentlyProcessed'] as Map<String, dynamic>;
    report.writeln('去重缓存: ${recentlyProcessed['count']}/${recentlyProcessed['maxCapacity']}');
    
    // 队列状态
    final queues = stats['queues'] as Map<String, dynamic>;
    report.writeln('队列状态: '
        '高优先级${queues['high']}, '
        '中优先级${queues['medium']}, '
        '低优先级${queues['low']}, '
        '批处理${queues['batch']}/$_maxBatchQueueSize, '
        '加载中${queues['loading']}');
    
    // Workers状态
    final workers = stats['workers'] as Map<String, dynamic>;
    report.writeln('Workers: ${workers['count']}个, '
        '已清理: ${workers['disposed']}');
    
    // 处理状态
    final status = stats['status'] as Map<String, dynamic>;
    report.writeln('处理状态: '
        '队列处理中: ${status['isProcessingQueue']}, '
        '批处理中: ${status['isBatchProcessing']}, '
        '低内存: ${status['isLowMemory']}, '
        'WiFi: ${status['isOnWifi']}');
    
    report.writeln('=== 报告结束 ===');
    
    return report.toString();
  }
  
  /// 清除所有缓存(内存和持久化)
  Future<void> clearAllCache() async {
    clearMemoryCache();
    try {
      await _cacheManager.clear(strategy: CacheStrategy.both);
      LogUtil.debug('已清除所有图片缓存');
    } catch (e) {
      LogUtil.error('清除图片缓存失败: $e');
    }
  }
  
  /// 检查图片是否已预加载
  bool isImagePreloaded(String url) {
    // 检查原始URL
    if (_imageCache.hasImage(url)) {
      return true;
    }

    // 检查各种质量级别
    for (var quality in ImagePreloadQuality.values) {
      final cacheKey = _generateCacheKey(url, quality, null, null);
      if (_imageCache.containsKey(cacheKey)) {
        return true;
      }
    }

    return false;
  }
  
  /// 获取已预加载的图片数量
  int get preloadedCount => _imageCache.preloadedCount;
  
  /// 获取正在加载的图片数量
  int get loadingCount => _loadingImages.length;
  
  /// 获取队列中的图片数量
  int get queueCount => 
      _highPriorityQueue.length + 
      _mediumPriorityQueue.length + 
      _lowPriorityQueue.length;
      
  /// 生成内存使用报告
  String _generateMemoryReport() {
    return '内存使用: '
        '图片缓存${_imageCache.length}/${_imageCache.maxCapacity}(${(_imageCache.totalSizeBytes/1024/1024).toStringAsFixed(1)}MB), '
        '去重缓存${_recentlyProcessed.length}/${_recentlyProcessed.maxCapacity}, '
        '队列总数${queueCount}, '
        '批处理${_batchQueue.length}/$_maxBatchQueueSize, '
        '加载中${_loadingImages.length}';
  }
      
  /// 获得详细的内存使用统计信息
  Map<String, dynamic> getMemoryStats() {
    return {
      'imageCache': {
        'count': _imageCache.length,
        'maxCapacity': _imageCache.maxCapacity,
        'sizeBytes': _imageCache.totalSizeBytes,
        'maxSizeBytes': _imageCache.maxSizeBytes,
        'preloadedCount': _imageCache.preloadedCount,
        'stats': _imageCache.stats.toString(),
      },
      'recentlyProcessed': {
        'count': _recentlyProcessed.length,
        'maxCapacity': _recentlyProcessed.maxCapacity,
        'stats': _recentlyProcessed.stats.toString(),
      },
      'queues': {
        'high': _highPriorityQueue.length,
        'medium': _mediumPriorityQueue.length,
        'low': _lowPriorityQueue.length,
        'batch': _batchQueue.length,
        'loading': _loadingImages.length,
      },
      'workers': {
        'count': _workers.length,
        'disposed': _workersDisposed,
      },
      'status': {
        'isProcessingQueue': _isProcessingQueue,
        'isBatchProcessing': _isBatchProcessing,
        'isLowMemory': _isLowMemory,
        'isOnWifi': _isOnWifi,
      }
    };
  }
  
  /// 根据图片URL获取最佳预加载质量
  ImagePreloadQuality getBestQuality(String url, {bool isVisible = false}) {
    // 基于网络状态和图片可见性决定最佳质量
    if (_isOnWifi) {
      return isVisible 
          ? ImagePreloadQuality.original 
          : ImagePreloadQuality.high;
    } else {
      return isVisible 
          ? ImagePreloadQuality.high 
          : ImagePreloadQuality.medium;
    }
  }
  
  /// 优化内存使用，清理低优先级缓存
  void optimizeMemoryUsage() {
    if (_isLowMemory) {
      // 内存压力大时，清理过期缓存
      final expiredCount = _imageCache.cleanupExpired();

      // 如果缓存仍然过多，LRU会自动淘汰最久未使用的项
      if (_imageCache.length > 50) {
        // 可以考虑调整缓存大小或清理策略
        LogUtil.debug('当前缓存项数量: ${_imageCache.length}，建议清理');
      }

      // 清空低优先级队列
      _lowPriorityQueue.clear();

      LogUtil.debug('内存优化：清理了${expiredCount}个过期缓存项，当前缓存: ${_imageCache.toString()}');
    }
  }
  
  /// 预测并预加载即将需要的图片
  void predictAndPreload(List<String> recentlyAccessedUrls, List<String> potentialNextUrls) {
    // 优先加载最近访问过的图片，可能会再次访问
    preloadImages(
      recentlyAccessedUrls,
      priority: ImagePreloadPriority.medium,
      quality: getBestQuality('', isVisible: false),
    );
    
    // 预加载可能即将访问的图片
    if (_isOnWifi && !_isLowMemory) {
      // 仅在Wifi环境和内存充足时预加载
      preloadImages(
        potentialNextUrls,
        priority: ImagePreloadPriority.low,
        quality: ImagePreloadQuality.medium,
      );
    }
  }
  
  /// 设置网络状态
  void setNetworkStatus(bool isWifi) {
    _isOnWifi = isWifi;
  }
  
  /// 设置内存状态
  void setMemoryStatus(bool isLowMemory) {
    _isLowMemory = isLowMemory;
    if (isLowMemory) {
      optimizeMemoryUsage();
    }
  }

  /// 清理所有资源
  void _clearAllResources() {
    try {
      LogUtil.debug('开始清理ImagePreloader资源...');

      // 1. 取消所有异步操作
      _mainCancellationToken.cancel();

      // 2. 停止批处理状态
      _isBatchProcessing = false;
      _isProcessingQueue = false;

      // 3. 清空批处理队列
      final batchCount = _batchQueue.length;
      _batchQueue.clear();

      // 4. 清空优先级队列
      final highCount = _highPriorityQueue.length;
      final mediumCount = _mediumPriorityQueue.length;
      final lowCount = _lowPriorityQueue.length;
      _highPriorityQueue.clear();
      _mediumPriorityQueue.clear();
      _lowPriorityQueue.clear();

      // 5. 清空加载中集合
      final loadingCount = _loadingImages.length;
      _loadingImages.clear();

      // 6. 清空去重LRU缓存
      final recentlyProcessedCount = _recentlyProcessed.length;
      _recentlyProcessed.clear();

      // 7. 清空图片LRU缓存
      final imageCacheInfo = _imageCache.toString();
      _imageCache.clear();

      // 8. 使用资源跟踪器清理剩余资源
      final resourceStats = _resourceTracker.getStats();
      if (resourceStats['totalResources'] > 0) {
        LogUtil.debug('通过ResourceTracker清理剩余${resourceStats['totalResources']}个资源');
        _resourceTracker.cleanupAll();
      }

      // 9. 记录清理结果
      LogUtil.debug('ImagePreloader资源清理完成: '
          '批处理队列:$batchCount项, '
          '优先级队列:高$highCount/中$mediumCount/低$lowCount, '
          '加载中:$loadingCount项, '
          '去重缓存:$recentlyProcessedCount项, '
          '图片缓存:$imageCacheInfo, '
          '资源跟踪器:${resourceStats['totalResources']}项');

    } catch (e) {
      LogUtil.error('ImagePreloader资源清理异常: $e');
    }
  }
  
  /// 检查资源清理是否完成
  bool _isResourcesCleaned() {
    // 检查基本队列和缓存
    final basicResourcesCleaned = _batchQueue.isEmpty &&
           _highPriorityQueue.isEmpty &&
           _mediumPriorityQueue.isEmpty &&
           _lowPriorityQueue.isEmpty &&
           _loadingImages.isEmpty &&
           _recentlyProcessed.isEmpty &&
           _imageCache.isEmpty;

    // 检查取消令牌状态
    final cancellationTokenCleaned = _mainCancellationToken.isCancelled;

    // 检查Workers状态
    final workersCleaned = _workersDisposed && _workers.isEmpty;

    // 检查资源跟踪器状态
    final resourceTrackerCleaned = !_resourceTracker.hasUncleanedResources;

    // 检查响应式变量状态（RxInt没有isClosed，通过其他方式检查）
    final rxVariablesCleaned = true; // RxInt会在onClose中被清理

    final allCleaned = basicResourcesCleaned &&
                      cancellationTokenCleaned &&
                      workersCleaned &&
                      resourceTrackerCleaned &&
                      rxVariablesCleaned;

    if (!allCleaned) {
      LogUtil.debug('资源清理检查结果: '
          'basic=$basicResourcesCleaned, '
          'cancellation=$cancellationTokenCleaned, '
          'workers=$workersCleaned, '
          'tracker=$resourceTrackerCleaned, '
          'rx=$rxVariablesCleaned');
    }

    return allCleaned;
  }

  /// 释放资源 - 重写GetxController的dispose方法
  @override
  void dispose() {
    LogUtil.debug('ImagePreloader.dispose() 开始分阶段清理...');

    try {
      // 阶段1: 立即取消所有异步操作
      _performPhase1Cleanup();

      // 阶段2: 清理队列和缓存
      _performPhase2Cleanup();

      // 阶段3: 清理Workers和响应式变量
      _performPhase3Cleanup();

      // 阶段4: 最终验证和强制清理
      _performPhase4Cleanup();

      super.dispose();
      LogUtil.debug('ImagePreloader.dispose() 完成');
    } catch (e) {
      LogUtil.error('ImagePreloader.dispose() 异常: $e');
      // 即使出现异常也要进行强制清理
      _performEmergencyCleanup();
      super.dispose();
    }
  }

  /// 阶段1: 立即取消所有异步操作
  void _performPhase1Cleanup() {
    LogUtil.debug('执行阶段1清理: 取消异步操作');

    // 取消主令牌，级联取消所有子操作
    if (!_mainCancellationToken.isCancelled) {
      _mainCancellationToken.cancel();
    }

    // 停止处理状态
    _isBatchProcessing = false;
    _isProcessingQueue = false;
  }

  /// 阶段2: 清理队列和缓存
  void _performPhase2Cleanup() {
    LogUtil.debug('执行阶段2清理: 清理队列和缓存');

    // 清空所有队列
    _batchQueue.clear();
    _highPriorityQueue.clear();
    _mediumPriorityQueue.clear();
    _lowPriorityQueue.clear();
    _loadingImages.clear();

    // 清空缓存
    _recentlyProcessed.clear();
    _imageCache.clear();
  }

  /// 阶段3: 清理Workers和响应式变量
  void _performPhase3Cleanup() {
    LogUtil.debug('执行阶段3清理: Workers和响应式变量');

    // 清理Workers
    _disposeWorkers();

    // 清理响应式变量
    try {
      _batchTrigger.close();
    } catch (e) {
      LogUtil.warn('关闭_batchTrigger异常: $e');
    }
  }

  /// 阶段4: 最终验证和强制清理
  void _performPhase4Cleanup() {
    LogUtil.debug('执行阶段4清理: 最终验证');

    // 使用资源跟踪器进行最终清理
    if (_resourceTracker.hasUncleanedResources) {
      LogUtil.warn('发现未清理资源: ${_resourceTracker.uncleanedResourceKeys}');
      _resourceTracker.cleanupAll();
    }

    // 从Finalizer分离
    try {
      _finalizer.detach(this);
    } catch (e) {
      LogUtil.warn('从Finalizer分离异常: $e');
    }

    // 最终验证
    if (!_isResourcesCleaned()) {
      LogUtil.error('资源清理验证失败，存在内存泄漏风险');
      _reportMemoryLeaks();
    } else {
      LogUtil.debug('所有资源已成功清理');
    }
  }

  /// 紧急清理 - 当正常清理失败时使用
  void _performEmergencyCleanup() {
    LogUtil.warn('执行紧急清理程序');

    try {
      // 强制取消所有操作
      _mainCancellationToken.cancel();

      // 强制清理资源跟踪器
      _resourceTracker.cleanupAll();

      // 强制清理Workers
      for (final worker in _workers) {
        try {
          if (!worker.disposed) {
            worker.dispose();
          }
        } catch (e) {
          LogUtil.error('紧急清理Worker异常: $e');
        }
      }
      _workers.clear();
      _workersDisposed = true;

      // 从Finalizer分离
      try {
        _finalizer.detach(this);
      } catch (e) {
        // 忽略分离异常
      }

    } catch (e) {
      LogUtil.error('紧急清理异常: $e');
    }
  }

  /// 报告内存泄露详情
  void _reportMemoryLeaks() {
    final leaks = <String>[];

    // 检查队列
    if (_batchQueue.isNotEmpty) {
      leaks.add('批处理队列: ${_batchQueue.length}项');
    }
    if (_highPriorityQueue.isNotEmpty) {
      leaks.add('高优先级队列: ${_highPriorityQueue.length}项');
    }
    if (_mediumPriorityQueue.isNotEmpty) {
      leaks.add('中优先级队列: ${_mediumPriorityQueue.length}项');
    }
    if (_lowPriorityQueue.isNotEmpty) {
      leaks.add('低优先级队列: ${_lowPriorityQueue.length}项');
    }
    if (_loadingImages.isNotEmpty) {
      leaks.add('加载中集合: ${_loadingImages.length}项');
    }

    // 检查缓存
    if (_recentlyProcessed.length > 0) {
      leaks.add('去重缓存: ${_recentlyProcessed.length}项');
    }
    if (_imageCache.length > 0) {
      leaks.add('图片缓存: ${_imageCache.length}项');
    }

    // 检查Workers
    if (!_workersDisposed || _workers.isNotEmpty) {
      leaks.add('Workers: disposed=$_workersDisposed, count=${_workers.length}');
    }

    // 检查取消令牌
    if (!_mainCancellationToken.isCancelled) {
      leaks.add('主取消令牌未取消');
    }

    // 检查资源跟踪器
    if (_resourceTracker.hasUncleanedResources) {
      leaks.add('资源跟踪器: ${_resourceTracker.uncleanedResourceKeys}');
    }

    if (leaks.isNotEmpty) {
      LogUtil.error('检测到内存泄露: ${leaks.join(', ')}');
    }
  }

  /// 获取详细的内存使用报告
  Map<String, dynamic> getMemoryReport() {
    return {
      'queues': {
        'batch': _batchQueue.length,
        'highPriority': _highPriorityQueue.length,
        'mediumPriority': _mediumPriorityQueue.length,
        'lowPriority': _lowPriorityQueue.length,
        'loading': _loadingImages.length,
      },
      'caches': {
        'recentlyProcessed': {
          'count': _recentlyProcessed.length,
          'maxCapacity': _recentlyProcessed.maxCapacity,
        },
        'imageCache': {
          'count': _imageCache.length,
          'maxCapacity': _imageCache.maxCapacity,
          'sizeBytes': _imageCache.totalSizeBytes,
          'maxSizeBytes': _imageCache.maxSizeBytes,
        },
      },
      'workers': {
        'count': _workers.length,
        'disposed': _workersDisposed,
      },
      'cancellation': {
        'mainTokenCancelled': _mainCancellationToken.isCancelled,
      },
      'resourceTracker': _resourceTracker.getStats(),
      'status': {
        'isBatchProcessing': _isBatchProcessing,
        'isProcessingQueue': _isProcessingQueue,
        'isLowMemory': _isLowMemory,
        'isOnWifi': _isOnWifi,
      },
      'resourcesCleaned': _isResourcesCleaned(),
    };
  }

  /// 强制垃圾回收（仅用于测试）
  void forceGarbageCollection() {
    // 在Dart中没有直接的强制GC方法，但可以创建大量临时对象来触发GC
    LogUtil.debug('尝试触发垃圾回收...');
    final temp = List.generate(1000, (i) => List.filled(1000, i));
    temp.clear();
  }
}

